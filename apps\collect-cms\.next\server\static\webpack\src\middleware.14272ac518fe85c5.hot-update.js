"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./node_modules/next/dist/esm/server/web/adapter.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/esm/server/web/adapter.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NextRequestHint: () => (/* binding */ NextRequestHint),\n/* harmony export */   adapter: () => (/* binding */ adapter)\n/* harmony export */ });\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./error */ \"(middleware)/./node_modules/next/dist/esm/server/web/error.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(middleware)/./node_modules/next/dist/esm/server/web/utils.js\");\n/* harmony import */ var _spec_extension_fetch_event__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./spec-extension/fetch-event */ \"(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/fetch-event.js\");\n/* harmony import */ var _spec_extension_request__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./spec-extension/request */ \"(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/request.js\");\n/* harmony import */ var _spec_extension_response__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./spec-extension/response */ \"(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/response.js\");\n/* harmony import */ var _shared_lib_router_utils_relativize_url__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../shared/lib/router/utils/relativize-url */ \"(middleware)/./node_modules/next/dist/esm/shared/lib/router/utils/relativize-url.js\");\n/* harmony import */ var _next_url__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./next-url */ \"(middleware)/./node_modules/next/dist/esm/server/web/next-url.js\");\n/* harmony import */ var _internal_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../internal-utils */ \"(middleware)/./node_modules/next/dist/esm/server/internal-utils.js\");\n/* harmony import */ var _shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../shared/lib/router/utils/app-paths */ \"(middleware)/./node_modules/next/dist/esm/shared/lib/router/utils/app-paths.js\");\n/* harmony import */ var _client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../client/components/app-router-headers */ \"(middleware)/./node_modules/next/dist/esm/client/components/app-router-headers.js\");\n/* harmony import */ var _globals__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./globals */ \"(middleware)/./node_modules/next/dist/esm/server/web/globals.js\");\n/* harmony import */ var _async_storage_request_async_storage_wrapper__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../async-storage/request-async-storage-wrapper */ \"(middleware)/./node_modules/next/dist/esm/server/async-storage/request-async-storage-wrapper.js\");\n/* harmony import */ var _client_components_request_async_storage_external__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../client/components/request-async-storage.external */ \"(shared)/./node_modules/next/dist/esm/client/components/request-async-storage.external.js\");\n/* harmony import */ var _lib_trace_tracer__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../lib/trace/tracer */ \"(middleware)/./node_modules/next/dist/esm/server/lib/trace/tracer.js\");\n/* harmony import */ var _lib_trace_constants__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../lib/trace/constants */ \"(middleware)/./node_modules/next/dist/esm/server/lib/trace/constants.js\");\n/* harmony import */ var _get_edge_preview_props__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./get-edge-preview-props */ \"(middleware)/./node_modules/next/dist/esm/server/web/get-edge-preview-props.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nclass NextRequestHint extends _spec_extension_request__WEBPACK_IMPORTED_MODULE_3__.NextRequest {\n    constructor(params){\n        super(params.input, params.init);\n        this.sourcePage = params.page;\n    }\n    get request() {\n        throw new _error__WEBPACK_IMPORTED_MODULE_0__.PageSignatureError({\n            page: this.sourcePage\n        });\n    }\n    respondWith() {\n        throw new _error__WEBPACK_IMPORTED_MODULE_0__.PageSignatureError({\n            page: this.sourcePage\n        });\n    }\n    waitUntil() {\n        throw new _error__WEBPACK_IMPORTED_MODULE_0__.PageSignatureError({\n            page: this.sourcePage\n        });\n    }\n}\nconst headersGetter = {\n    keys: (headers)=>Array.from(headers.keys()),\n    get: (headers, key)=>headers.get(key) ?? undefined\n};\nlet propagator = (request, fn)=>{\n    const tracer = (0,_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_13__.getTracer)();\n    return tracer.withPropagatedContext(request.headers, fn, headersGetter);\n};\nlet testApisIntercepted = false;\nfunction ensureTestApisIntercepted() {\n    if (!testApisIntercepted) {\n        testApisIntercepted = true;\n        if (process.env.NEXT_PRIVATE_TEST_PROXY === \"true\") {\n            const { interceptTestApis, wrapRequestHandler } = __webpack_require__(/*! next/dist/experimental/testmode/server-edge */ \"(middleware)/./node_modules/next/dist/experimental/testmode/server-edge.js\");\n            interceptTestApis();\n            propagator = wrapRequestHandler(propagator);\n        }\n    }\n}\nasync function adapter(params) {\n    ensureTestApisIntercepted();\n    await (0,_globals__WEBPACK_IMPORTED_MODULE_10__.ensureInstrumentationRegistered)();\n    // TODO-APP: use explicit marker for this\n    const isEdgeRendering = typeof self.__BUILD_MANIFEST !== \"undefined\";\n    params.request.url = (0,_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_8__.normalizeRscURL)(params.request.url);\n    const requestUrl = new _next_url__WEBPACK_IMPORTED_MODULE_6__.NextURL(params.request.url, {\n        headers: params.request.headers,\n        nextConfig: params.request.nextConfig\n    });\n    // Iterator uses an index to keep track of the current iteration. Because of deleting and appending below we can't just use the iterator.\n    // Instead we use the keys before iteration.\n    const keys = [\n        ...requestUrl.searchParams.keys()\n    ];\n    for (const key of keys){\n        const value = requestUrl.searchParams.getAll(key);\n        (0,_utils__WEBPACK_IMPORTED_MODULE_1__.normalizeNextQueryParam)(key, (normalizedKey)=>{\n            requestUrl.searchParams.delete(normalizedKey);\n            for (const val of value){\n                requestUrl.searchParams.append(normalizedKey, val);\n            }\n            requestUrl.searchParams.delete(key);\n        });\n    }\n    // Ensure users only see page requests, never data requests.\n    const buildId = requestUrl.buildId;\n    requestUrl.buildId = \"\";\n    const isNextDataRequest = params.request.headers[\"x-nextjs-data\"];\n    if (isNextDataRequest && requestUrl.pathname === \"/index\") {\n        requestUrl.pathname = \"/\";\n    }\n    const requestHeaders = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.fromNodeOutgoingHttpHeaders)(params.request.headers);\n    const flightHeaders = new Map();\n    // Parameters should only be stripped for middleware\n    if (!isEdgeRendering) {\n        for (const param of _client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_9__.FLIGHT_PARAMETERS){\n            const key = param.toString().toLowerCase();\n            const value = requestHeaders.get(key);\n            if (value) {\n                flightHeaders.set(key, requestHeaders.get(key));\n                requestHeaders.delete(key);\n            }\n        }\n    }\n    const normalizeUrl =  false ? 0 : requestUrl;\n    const request = new NextRequestHint({\n        page: params.page,\n        // Strip internal query parameters off the request.\n        input: (0,_internal_utils__WEBPACK_IMPORTED_MODULE_7__.stripInternalSearchParams)(normalizeUrl, true).toString(),\n        init: {\n            body: params.request.body,\n            geo: params.request.geo,\n            headers: requestHeaders,\n            ip: params.request.ip,\n            method: params.request.method,\n            nextConfig: params.request.nextConfig,\n            signal: params.request.signal\n        }\n    });\n    /**\n   * This allows to identify the request as a data request. The user doesn't\n   * need to know about this property neither use it. We add it for testing\n   * purposes.\n   */ if (isNextDataRequest) {\n        Object.defineProperty(request, \"__isData\", {\n            enumerable: false,\n            value: true\n        });\n    }\n    if (!globalThis.__incrementalCache && params.IncrementalCache) {\n        globalThis.__incrementalCache = new params.IncrementalCache({\n            appDir: true,\n            fetchCache: true,\n            minimalMode: \"development\" !== \"development\",\n            fetchCacheKeyPrefix: \"\",\n            dev: \"development\" === \"development\",\n            requestHeaders: params.request.headers,\n            requestProtocol: \"https\",\n            getPrerenderManifest: ()=>{\n                return {\n                    version: -1,\n                    routes: {},\n                    dynamicRoutes: {},\n                    notFoundRoutes: [],\n                    preview: (0,_get_edge_preview_props__WEBPACK_IMPORTED_MODULE_15__.getEdgePreviewProps)()\n                };\n            }\n        });\n    }\n    const event = new _spec_extension_fetch_event__WEBPACK_IMPORTED_MODULE_2__.NextFetchEvent({\n        request,\n        page: params.page\n    });\n    let response;\n    let cookiesFromResponse;\n    response = await propagator(request, ()=>{\n        // we only care to make async storage available for middleware\n        const isMiddleware = params.page === \"/middleware\" || params.page === \"/src/middleware\";\n        if (isMiddleware) {\n            return (0,_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_13__.getTracer)().trace(_lib_trace_constants__WEBPACK_IMPORTED_MODULE_14__.MiddlewareSpan.execute, {\n                spanName: `middleware ${request.method} ${request.nextUrl.pathname}`,\n                attributes: {\n                    \"http.target\": request.nextUrl.pathname,\n                    \"http.method\": request.method\n                }\n            }, ()=>_async_storage_request_async_storage_wrapper__WEBPACK_IMPORTED_MODULE_11__.RequestAsyncStorageWrapper.wrap(_client_components_request_async_storage_external__WEBPACK_IMPORTED_MODULE_12__.requestAsyncStorage, {\n                    req: request,\n                    renderOpts: {\n                        onUpdateCookies: (cookies)=>{\n                            cookiesFromResponse = cookies;\n                        },\n                        // @ts-expect-error: TODO: investigate why previewProps isn't on RenderOpts\n                        previewProps: (0,_get_edge_preview_props__WEBPACK_IMPORTED_MODULE_15__.getEdgePreviewProps)()\n                    }\n                }, ()=>params.handler(request, event)));\n        }\n        return params.handler(request, event);\n    });\n    // check if response is a Response object\n    if (response && !(response instanceof Response)) {\n        throw new TypeError(\"Expected an instance of Response to be returned\");\n    }\n    if (response && cookiesFromResponse) {\n        response.headers.set(\"set-cookie\", cookiesFromResponse);\n    }\n    /**\n   * For rewrites we must always include the locale in the final pathname\n   * so we re-create the NextURL forcing it to include it when the it is\n   * an internal rewrite. Also we make sure the outgoing rewrite URL is\n   * a data URL if the request was a data request.\n   */ const rewrite = response == null ? void 0 : response.headers.get(\"x-middleware-rewrite\");\n    if (response && rewrite && !isEdgeRendering) {\n        const rewriteUrl = new _next_url__WEBPACK_IMPORTED_MODULE_6__.NextURL(rewrite, {\n            forceLocale: true,\n            headers: params.request.headers,\n            nextConfig: params.request.nextConfig\n        });\n        if (true) {\n            if (rewriteUrl.host === request.nextUrl.host) {\n                rewriteUrl.buildId = buildId || rewriteUrl.buildId;\n                response.headers.set(\"x-middleware-rewrite\", String(rewriteUrl));\n            }\n        }\n        /**\n     * When the request is a data request we must show if there was a rewrite\n     * with an internal header so the client knows which component to load\n     * from the data request.\n     */ const relativizedRewrite = (0,_shared_lib_router_utils_relativize_url__WEBPACK_IMPORTED_MODULE_5__.relativizeURL)(String(rewriteUrl), String(requestUrl));\n        if (isNextDataRequest && // if the rewrite is external and external rewrite\n        // resolving config is enabled don't add this header\n        // so the upstream app can set it instead\n        !( false && 0)) {\n            response.headers.set(\"x-nextjs-rewrite\", relativizedRewrite);\n        }\n    }\n    /**\n   * For redirects we will not include the locale in case when it is the\n   * default and we must also make sure the outgoing URL is a data one if\n   * the incoming request was a data request.\n   */ const redirect = response == null ? void 0 : response.headers.get(\"Location\");\n    if (response && redirect && !isEdgeRendering) {\n        const redirectURL = new _next_url__WEBPACK_IMPORTED_MODULE_6__.NextURL(redirect, {\n            forceLocale: false,\n            headers: params.request.headers,\n            nextConfig: params.request.nextConfig\n        });\n        /**\n     * Responses created from redirects have immutable headers so we have\n     * to clone the response to be able to modify it.\n     */ response = new Response(response.body, response);\n        if (true) {\n            if (redirectURL.host === request.nextUrl.host) {\n                redirectURL.buildId = buildId || redirectURL.buildId;\n                response.headers.set(\"Location\", String(redirectURL));\n            }\n        }\n        /**\n     * When the request is a data request we can't use the location header as\n     * it may end up with CORS error. Instead we map to an internal header so\n     * the client knows the destination.\n     */ if (isNextDataRequest) {\n            response.headers.delete(\"Location\");\n            response.headers.set(\"x-nextjs-redirect\", (0,_shared_lib_router_utils_relativize_url__WEBPACK_IMPORTED_MODULE_5__.relativizeURL)(String(redirectURL), String(requestUrl)));\n        }\n    }\n    const finalResponse = response ? response : _spec_extension_response__WEBPACK_IMPORTED_MODULE_4__.NextResponse.next();\n    // Flight headers are not overridable / removable so they are applied at the end.\n    const middlewareOverrideHeaders = finalResponse.headers.get(\"x-middleware-override-headers\");\n    const overwrittenHeaders = [];\n    if (middlewareOverrideHeaders) {\n        for (const [key, value] of flightHeaders){\n            finalResponse.headers.set(`x-middleware-request-${key}`, value);\n            overwrittenHeaders.push(key);\n        }\n        if (overwrittenHeaders.length > 0) {\n            finalResponse.headers.set(\"x-middleware-override-headers\", middlewareOverrideHeaders + \",\" + overwrittenHeaders.join(\",\"));\n        }\n    }\n    return {\n        response: finalResponse,\n        waitUntil: Promise.all(event[_spec_extension_fetch_event__WEBPACK_IMPORTED_MODULE_2__.waitUntilSymbol]),\n        fetchMetrics: request.fetchMetrics\n    };\n}\n\n//# sourceMappingURL=adapter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./node_modules/next/dist/esm/server/web/adapter.js\n");

/***/ }),

/***/ "(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/adapters/request-cookies.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next/dist/esm/server/web/spec-extension/adapters/request-cookies.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MutableRequestCookiesAdapter: () => (/* binding */ MutableRequestCookiesAdapter),\n/* harmony export */   ReadonlyRequestCookiesError: () => (/* binding */ ReadonlyRequestCookiesError),\n/* harmony export */   RequestCookiesAdapter: () => (/* binding */ RequestCookiesAdapter),\n/* harmony export */   appendMutableCookies: () => (/* binding */ appendMutableCookies),\n/* harmony export */   getModifiedCookieValues: () => (/* binding */ getModifiedCookieValues)\n/* harmony export */ });\n/* harmony import */ var _cookies__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../cookies */ \"(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/cookies.js\");\n/* harmony import */ var _reflect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./reflect */ \"(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/adapters/reflect.js\");\n/* harmony import */ var _client_components_static_generation_async_storage_external__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../../client/components/static-generation-async-storage.external */ \"(shared)/./node_modules/next/dist/esm/client/components/static-generation-async-storage.external.js\");\n\n\n\n/**\n * @internal\n */ class ReadonlyRequestCookiesError extends Error {\n    constructor(){\n        super(\"Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options\");\n    }\n    static callable() {\n        throw new ReadonlyRequestCookiesError();\n    }\n}\nclass RequestCookiesAdapter {\n    static seal(cookies) {\n        return new Proxy(cookies, {\n            get (target, prop, receiver) {\n                switch(prop){\n                    case \"clear\":\n                    case \"delete\":\n                    case \"set\":\n                        return ReadonlyRequestCookiesError.callable;\n                    default:\n                        return _reflect__WEBPACK_IMPORTED_MODULE_1__.ReflectAdapter.get(target, prop, receiver);\n                }\n            }\n        });\n    }\n}\nconst SYMBOL_MODIFY_COOKIE_VALUES = Symbol.for(\"next.mutated.cookies\");\nfunction getModifiedCookieValues(cookies) {\n    const modified = cookies[SYMBOL_MODIFY_COOKIE_VALUES];\n    if (!modified || !Array.isArray(modified) || modified.length === 0) {\n        return [];\n    }\n    return modified;\n}\nfunction appendMutableCookies(headers, mutableCookies) {\n    const modifiedCookieValues = getModifiedCookieValues(mutableCookies);\n    if (modifiedCookieValues.length === 0) {\n        return false;\n    }\n    // Return a new response that extends the response with\n    // the modified cookies as fallbacks. `res` cookies\n    // will still take precedence.\n    const resCookies = new _cookies__WEBPACK_IMPORTED_MODULE_0__.ResponseCookies(headers);\n    const returnedCookies = resCookies.getAll();\n    // Set the modified cookies as fallbacks.\n    for (const cookie of modifiedCookieValues){\n        resCookies.set(cookie);\n    }\n    // Set the original cookies as the final values.\n    for (const cookie of returnedCookies){\n        resCookies.set(cookie);\n    }\n    return true;\n}\nclass MutableRequestCookiesAdapter {\n    static wrap(cookies, onUpdateCookies) {\n        const responseCookies = new _cookies__WEBPACK_IMPORTED_MODULE_0__.ResponseCookies(new Headers());\n        for (const cookie of cookies.getAll()){\n            responseCookies.set(cookie);\n        }\n        let modifiedValues = [];\n        const modifiedCookies = new Set();\n        const updateResponseCookies = ()=>{\n            // TODO-APP: change method of getting staticGenerationAsyncStore\n            const staticGenerationAsyncStore = _client_components_static_generation_async_storage_external__WEBPACK_IMPORTED_MODULE_2__.staticGenerationAsyncStorage.getStore();\n            if (staticGenerationAsyncStore) {\n                staticGenerationAsyncStore.pathWasRevalidated = true;\n            }\n            const allCookies = responseCookies.getAll();\n            modifiedValues = allCookies.filter((c)=>modifiedCookies.has(c.name));\n            if (onUpdateCookies) {\n                const serializedCookies = [];\n                for (const cookie of modifiedValues){\n                    const tempCookies = new _cookies__WEBPACK_IMPORTED_MODULE_0__.ResponseCookies(new Headers());\n                    tempCookies.set(cookie);\n                    serializedCookies.push(tempCookies.toString());\n                }\n                onUpdateCookies(serializedCookies);\n            }\n        };\n        return new Proxy(responseCookies, {\n            get (target, prop, receiver) {\n                switch(prop){\n                    // A special symbol to get the modified cookie values\n                    case SYMBOL_MODIFY_COOKIE_VALUES:\n                        return modifiedValues;\n                    // TODO: Throw error if trying to set a cookie after the response\n                    // headers have been set.\n                    case \"delete\":\n                        return function(...args) {\n                            modifiedCookies.add(typeof args[0] === \"string\" ? args[0] : args[0].name);\n                            try {\n                                target.delete(...args);\n                            } finally{\n                                updateResponseCookies();\n                            }\n                        };\n                    case \"set\":\n                        return function(...args) {\n                            modifiedCookies.add(typeof args[0] === \"string\" ? args[0] : args[0].name);\n                            try {\n                                return target.set(...args);\n                            } finally{\n                                updateResponseCookies();\n                            }\n                        };\n                    default:\n                        return _reflect__WEBPACK_IMPORTED_MODULE_1__.ReflectAdapter.get(target, prop, receiver);\n                }\n            }\n        });\n    }\n}\n\n//# sourceMappingURL=request-cookies.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./node_modules/next/dist/esm/server/web/spec-extension/adapters/request-cookies.js\n");

/***/ })

});