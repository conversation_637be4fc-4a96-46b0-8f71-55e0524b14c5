"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(pagebuilder)/content-builder/[...slug]/layout",{

/***/ "(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/Media.tsx":
/*!********************************************************************!*\
  !*** ./src/components/Builder/FieldEditor/regular/Media/Media.tsx ***!
  \********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Media: function() { return /* binding */ Media; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @swc/helpers/_/_async_to_generator */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_async_to_generator.js\");\n/* harmony import */ var _swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/_/_define_property */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_define_property.js\");\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var _swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @swc/helpers/_/_to_consumable_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_to_consumable_array.js\");\n/* harmony import */ var _swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @swc/helpers/_/_ts_generator */ \"(app-pages-browser)/../../node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,Input,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/hooks/useIsomorphicLayoutEffect.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,Input,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,Input,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,Input,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Image/ImageV2.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Icon,Image,Input,useIsomorphicLayoutEffect!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Input/Input.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/../../node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../../../../contexts/BuilderContext */ \"(app-pages-browser)/./src/contexts/BuilderContext.tsx\");\n/* harmony import */ var _media_module_scss__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./media.module.scss */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/media.module.scss\");\n/* harmony import */ var _media_module_scss__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_media_module_scss__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n\n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nvar formatDate = function(date) {\n    return dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date).format(\"D/M/YYYY\");\n};\nvar formatExt = function(ext) {\n    return ext.replace(\".\", \"\");\n};\nvar MAX_FILE_SIZE = 20 * 1024 * 1024 // 20MB in bytes\n;\nvar Media = function(props) {\n    _s();\n    var _ref = props !== null && props !== void 0 ? props : {}, value = _ref.value, onChange = _ref.onChange, multiple = _ref.multiple;\n    var pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_4__.useContext)(_contexts_BuilderContext__WEBPACK_IMPORTED_MODULE_6__.PageBuilderContext);\n    var mediaInfoState = context.mediaInfoState, setMediaInfoState = context.setMediaInfoState;\n    // Generate unique ID for this media component instance\n    var mediaId = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function() {\n        return \"\".concat(props.field || \"media\", \"_\").concat(Date.now(), \"_\").concat(Math.random());\n    }, [\n        props.field\n    ]);\n    // Local state for this component's data\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_7__._)((0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(value), 2), propsValue = _useState[0], setPropsValue = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_7__._)((0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(Array.isArray(propsValue) ? propsValue[0] || {} : propsValue || {}), 2), currentMedia = _useState1[0], setCurrentMedia = _useState1[1];\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_7__._)((0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0), 2), currentMediaIdx = _useState2[0], setCurrentMediaIdx = _useState2[1];\n    // Check if this component is currently being edited\n    var isEdit = mediaInfoState.isActive && mediaInfoState.mediaId === mediaId;\n    var fixedInfo = isEdit ? mediaInfoState.fixedInfo : {};\n    var editableInfo = isEdit ? mediaInfoState.editableInfo : {};\n    // Sync shared state back to local state when this component is being edited\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function() {\n        if (isEdit && mediaInfoState.propsValue && mediaInfoState.currentMedia) {\n            setPropsValue(mediaInfoState.propsValue);\n            setCurrentMedia(mediaInfoState.currentMedia);\n            setCurrentMediaIdx(mediaInfoState.currentMediaIdx);\n        }\n    }, [\n        isEdit,\n        mediaInfoState\n    ]);\n    // Navigation handlers\n    var handleNextMedia = function() {\n        if (Array.isArray(propsValue) && propsValue.length > 0) {\n            var newIdx = currentMediaIdx + 1 < propsValue.length ? currentMediaIdx + 1 : 0;\n            setCurrentMediaIdx(newIdx);\n            // Update shared state if this component is being edited\n            if (isEdit) {\n                setMediaInfoState(function(prev) {\n                    return (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_8__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_9__._)({}, prev), {\n                        currentMediaIdx: newIdx,\n                        currentMedia: propsValue[newIdx]\n                    });\n                });\n            }\n        }\n    };\n    var handlePrevMedia = function() {\n        if (Array.isArray(propsValue) && propsValue.length > 0) {\n            var newIdx = currentMediaIdx - 1 >= 0 ? currentMediaIdx - 1 : propsValue.length - 1;\n            setCurrentMediaIdx(newIdx);\n            // Update shared state if this component is being edited\n            if (isEdit) {\n                setMediaInfoState(function(prev) {\n                    return (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_8__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_9__._)({}, prev), {\n                        currentMediaIdx: newIdx,\n                        currentMedia: propsValue[newIdx]\n                    });\n                });\n            }\n        }\n    };\n    // Update current media when index or props change\n    (0,_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect)(function() {\n        if (Array.isArray(propsValue)) {\n            var newMedia = propsValue[currentMediaIdx] || {};\n            setCurrentMedia(newMedia);\n            // Update shared state if this component is being edited\n            if (isEdit) {\n                setMediaInfoState(function(prev) {\n                    return (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_8__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_9__._)({}, prev), {\n                        currentMedia: newMedia\n                    });\n                });\n            }\n        } else {\n            setCurrentMedia(propsValue);\n            // Update shared state if this component is being edited\n            if (isEdit) {\n                setMediaInfoState(function(prev) {\n                    return (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_8__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_9__._)({}, prev), {\n                        currentMedia: propsValue\n                    });\n                });\n            }\n        }\n    }, [\n        currentMediaIdx,\n        propsValue\n    ]);\n    // Media toolbar configuration\n    var mediaToolbar = [\n        {\n            name: \"Add\",\n            icon: \"add\",\n            action: \"add\",\n            visible: !multiple\n        },\n        {\n            name: \"Replace\",\n            icon: \"replace\",\n            action: \"replace\"\n        },\n        {\n            name: \"Duplicate\",\n            icon: \"duplicate\",\n            action: \"duplicate\",\n            visible: !multiple\n        },\n        {\n            name: \"Remove\",\n            icon: \"remove\",\n            action: \"remove\"\n        },\n        {\n            name: \"Download\",\n            icon: \"download\",\n            action: \"download\",\n            visible: !isEdit\n        }\n    ];\n    var filteredMediaToolbar = mediaToolbar.filter(function(tool) {\n        return !tool.visible;\n    });\n    var handleShowDetail = function() {\n        var size = currentMedia.size, width = currentMedia.width, height = currentMedia.height, publishedAt = currentMedia.publishedAt, ext = currentMedia.ext, name = currentMedia.name, alternativeText = currentMedia.alternativeText, caption = currentMedia.caption;\n        // console.log(currentMedia, name)\n        setMediaInfoState({\n            isActive: true,\n            mediaId: mediaId,\n            fixedInfo: {\n                size: \"\".concat(size, \"KB\"),\n                dimensions: \"\".concat(width, \"X\").concat(height),\n                date: formatDate(publishedAt),\n                extension: formatExt(ext || \"\")\n            },\n            editableInfo: {\n                fileName: name === null || name === void 0 ? void 0 : name.split(\".\").slice(0, -1).join(\".\"),\n                altText: alternativeText,\n                caption: caption\n            },\n            currentMedia: currentMedia,\n            currentMediaIdx: currentMediaIdx,\n            propsValue: propsValue,\n            field: props.field || \"\",\n            onChange: onChange\n        });\n    };\n    var handleBack = function() {\n        setMediaInfoState({\n            isActive: false,\n            mediaId: null,\n            fixedInfo: {},\n            editableInfo: {},\n            currentMedia: null,\n            currentMediaIdx: 0,\n            propsValue: null,\n            field: \"\",\n            onChange: undefined\n        });\n    };\n    var handleOnChange = function(e) {\n        var _e_target = e.target, name = _e_target.name, value = _e_target.value;\n        setMediaInfoState(function(prev) {\n            return (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_8__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_9__._)({}, prev), {\n                editableInfo: (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_8__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_9__._)({}, prev.editableInfo), (0,_swc_helpers_define_property__WEBPACK_IMPORTED_MODULE_11__._)({}, name, value))\n            });\n        });\n    };\n    var handleAction = function(key) {\n        switch(key){\n            case \"add\":\n                handleAdd();\n                break;\n            case \"replace\":\n                handleReplace();\n                break;\n            case \"duplicate\":\n                handleDuplicate();\n                break;\n            case \"remove\":\n                handleRemove();\n                break;\n            case \"download\":\n                handleDownload();\n                break;\n            default:\n                break;\n        }\n    };\n    // File input utility - creates file input and handles file selection\n    var createFileInput = function(callback) {\n        var input = document.createElement(\"input\");\n        input.type = \"file\";\n        input.accept = \"image/*\";\n        input.onchange = function(e) {\n            var target = e.target;\n            if (target.files && target.files.length > 0) {\n                var file = target.files[0];\n                if (file) {\n                    if (file.size > MAX_FILE_SIZE) {\n                        console.log(\"Exceeds the allowed media size limit of 20MB!\");\n                        return;\n                    }\n                    callback(file);\n                }\n            }\n        };\n        input.click();\n    };\n    // Process selected file and convert to MediaAttType\n    var processFile = function(file) {\n        return new Promise(function(resolve) {\n            var reader = new FileReader();\n            reader.onload = function(e) {\n                var _e_target;\n                var img = document.createElement(\"img\");\n                img.onload = function() {\n                    var _e_target;\n                    var now = new Date().toISOString();\n                    var ext = \".\" + file.name.split(\".\").pop();\n                    resolve({\n                        name: file.name,\n                        ext: ext,\n                        size: (file.size / 1024).toFixed(2),\n                        width: img.width,\n                        height: img.height,\n                        publishedAt: now,\n                        url: (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result,\n                        alternativeText: \"\",\n                        caption: \"\"\n                    });\n                };\n                img.src = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n            };\n            reader.readAsDataURL(file);\n        });\n    };\n    // Helper function to update media state and notify parent\n    var updateMediaState = function(newValue, newIndex) {\n        if (Array.isArray(newValue)) {\n            setPropsValue(newValue);\n            if (newIndex !== undefined) {\n                setCurrentMediaIdx(newIndex);\n                setCurrentMedia(newValue[newIndex] || {});\n            }\n        } else {\n            setCurrentMedia(newValue);\n            setPropsValue(newValue);\n        }\n        onChange === null || onChange === void 0 ? void 0 : onChange({\n            field: props.field || \"\",\n            value: JSON.stringify(newValue)\n        });\n    };\n    var handleAdd = function() {\n        createFileInput(function() {\n            var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_12__._)(function(file) {\n                var newMedia, newPropsValue;\n                return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_13__.__generator)(this, function(_state) {\n                    switch(_state.label){\n                        case 0:\n                            return [\n                                4,\n                                processFile(file)\n                            ];\n                        case 1:\n                            newMedia = _state.sent();\n                            if (Array.isArray(propsValue)) {\n                                newPropsValue = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_14__._)(propsValue).concat([\n                                    newMedia\n                                ]);\n                                updateMediaState(newPropsValue, newPropsValue.length - 1);\n                            } else {\n                                updateMediaState(newMedia);\n                            }\n                            return [\n                                2\n                            ];\n                    }\n                });\n            });\n            return function(file) {\n                return _ref.apply(this, arguments);\n            };\n        }());\n    };\n    var handleReplace = function() {\n        createFileInput(function() {\n            var _ref = (0,_swc_helpers_async_to_generator__WEBPACK_IMPORTED_MODULE_12__._)(function(file) {\n                var newMedia, newPropsValue;\n                return (0,_swc_helpers_ts_generator__WEBPACK_IMPORTED_MODULE_13__.__generator)(this, function(_state) {\n                    switch(_state.label){\n                        case 0:\n                            return [\n                                4,\n                                processFile(file)\n                            ];\n                        case 1:\n                            newMedia = _state.sent();\n                            if (Array.isArray(propsValue)) {\n                                newPropsValue = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_14__._)(propsValue);\n                                newPropsValue[currentMediaIdx] = newMedia;\n                                updateMediaState(newPropsValue, currentMediaIdx);\n                            } else {\n                                updateMediaState(newMedia);\n                            }\n                            return [\n                                2\n                            ];\n                    }\n                });\n            });\n            return function(file) {\n                return _ref.apply(this, arguments);\n            };\n        }());\n    };\n    var handleDuplicate = function() {\n        if (!currentMedia) return;\n        var duplicatedMedia = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_8__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_9__._)({}, currentMedia), {\n            publishedAt: new Date().toISOString()\n        });\n        if (Array.isArray(propsValue)) {\n            var newPropsValue = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_14__._)(propsValue).concat([\n                duplicatedMedia\n            ]);\n            updateMediaState(newPropsValue, newPropsValue.length - 1);\n        } else {\n            var newPropsValue1 = [\n                propsValue,\n                duplicatedMedia\n            ];\n            setPropsValue(newPropsValue1);\n            setCurrentMediaIdx(1);\n            setCurrentMedia(duplicatedMedia);\n            onChange === null || onChange === void 0 ? void 0 : onChange({\n                field: props.field || \"\",\n                value: JSON.stringify(newPropsValue1)\n            });\n        }\n    };\n    var handleRemove = function() {\n        if (!currentMedia) return;\n        if (Array.isArray(propsValue)) {\n            var newPropsValue = propsValue.filter(function(_, idx) {\n                return idx !== currentMediaIdx;\n            });\n            if (newPropsValue.length === 0) {\n                setCurrentMedia(null);\n                setPropsValue(null);\n                onChange === null || onChange === void 0 ? void 0 : onChange({\n                    field: props.field || \"\",\n                    value: \"\"\n                });\n            } else {\n                var newIdx = currentMediaIdx >= newPropsValue.length ? newPropsValue.length - 1 : currentMediaIdx;\n                updateMediaState(newPropsValue, newIdx);\n            }\n        } else {\n            setCurrentMedia(null);\n            setPropsValue(null);\n            onChange === null || onChange === void 0 ? void 0 : onChange({\n                field: props.field || \"\",\n                value: \"\"\n            });\n        }\n    };\n    var handleDownload = function() {\n        if (!currentMedia || !currentMedia.url) return;\n        var link = document.createElement(\"a\");\n        var url = currentMedia.url.startsWith(\"data:\") ? currentMedia.url : \"\".concat(\"https://ai-digital-brand-cms-smooth.gocollectives.com\").concat(currentMedia.url, \"?original=true&download=true\");\n        link.href = url;\n        link.download = currentMedia.name || \"download\";\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n    };\n    var handleSaveMediaInfo = function() {\n        if (!currentMedia || !isEdit) return;\n        var updatedMedia = (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_8__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_9__._)({}, currentMedia), {\n            name: editableInfo.fileName ? \"\".concat(editableInfo.fileName).concat(currentMedia.ext || \"\") : currentMedia.name,\n            alternativeText: editableInfo.altText || currentMedia.alternativeText,\n            caption: editableInfo.caption || currentMedia.caption\n        });\n        if (Array.isArray(propsValue)) {\n            var newPropsValue = (0,_swc_helpers_to_consumable_array__WEBPACK_IMPORTED_MODULE_14__._)(propsValue);\n            newPropsValue[currentMediaIdx] = updatedMedia;\n            updateMediaState(newPropsValue, currentMediaIdx);\n        } else {\n            updateMediaState(updatedMedia);\n        }\n        handleBack();\n    };\n    var isBuilderMode = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function() {\n        return pathname === null || pathname === void 0 ? void 0 : pathname.startsWith(\"/content-builder/\");\n    }, [\n        pathname\n    ]);\n    // Media item component to reduce code duplication\n    var MediaItem = function(param) {\n        var media = param.media, height = param.height;\n        if (!media) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().empty),\n                style: {\n                    \"--height\": height\n                },\n                title: \"Browse file(s)\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                        type: \"cms\",\n                        variant: \"image\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 6\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            \"Drop your file(s) here or \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                onClick: function() {\n                                    return handleAction(\"add\");\n                                },\n                                children: \"browse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 33\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 6\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                        children: \"Max. File Size: 20MB\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                        lineNumber: 412,\n                        columnNumber: 6\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                lineNumber: 403,\n                columnNumber: 5\n            }, _this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().item),\n            style: {\n                \"--height\": height\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().tag),\n                    children: formatExt((media === null || media === void 0 ? void 0 : media.ext) || \"\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 5\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().thumbnail),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_17__.Image, {\n                        media: media,\n                        alt: \"\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 6\n                    }, _this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                    lineNumber: 420,\n                    columnNumber: 5\n                }, _this),\n                !isEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().mask),\n                    title: \"Edit this media\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                        onClick: function() {\n                            return handleShowDetail();\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                            type: \"cms\",\n                            variant: \"edit\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                            lineNumber: 426,\n                            columnNumber: 8\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 7\n                    }, _this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                    lineNumber: 424,\n                    columnNumber: 6\n                }, _this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n            lineNumber: 418,\n            columnNumber: 4\n        }, _this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().wrapper),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().controller),\n                style: {\n                    \"--controller-cols\": isBuilderMode ? 12 : 8\n                },\n                children: [\n                    multiple && !isEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().nav),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().nav__btn),\n                                onClick: handlePrevMedia,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                    type: \"cms\",\n                                    variant: \"chevron-left\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 8\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 7\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().nav__index),\n                                children: \"\".concat(currentMediaIdx + 1, \"/\").concat(Array.isArray(propsValue) ? propsValue.length : 0)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 7\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().nav__btn),\n                                onClick: handleNextMedia,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                    type: \"cms\",\n                                    variant: \"chevron-right\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 8\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 7\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                        lineNumber: 441,\n                        columnNumber: 6\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().body), !isBuilderMode && isEdit ? multiple ? (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().detailed__multi) : (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().detailed) : \"\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MediaItem, {\n                                media: currentMedia,\n                                height: isBuilderMode ? \"160px\" : \"324px\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 6\n                            }, _this),\n                            isEdit && Array.isArray(propsValue) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items__nav),\n                                        onClick: handlePrevMedia,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                            type: \"cms\",\n                                            variant: \"chevron-left\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 9\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 8\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items__list),\n                                        children: propsValue.map(function(media, idx) {\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items__thumb), idx === currentMediaIdx ? (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().active) : \"\"),\n                                                onClick: function() {\n                                                    setCurrentMediaIdx(idx);\n                                                    // Update shared state if this component is being edited\n                                                    if (isEdit) {\n                                                        setMediaInfoState(function(prev) {\n                                                            return (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_8__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_9__._)({}, prev), {\n                                                                currentMediaIdx: idx,\n                                                                currentMedia: propsValue[idx]\n                                                            });\n                                                        });\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_17__.Image, {\n                                                    media: media,\n                                                    alt: \"\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 11\n                                                }, _this)\n                                            }, idx, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 10\n                                            }, _this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 8\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items__nav),\n                                        onClick: handleNextMedia,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                            type: \"cms\",\n                                            variant: \"chevron-right\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 9\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 8\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 7\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 5\n                    }, _this),\n                    !isBuilderMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__list),\n                                children: filteredMediaToolbar.map(function(tool, idx) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__button),\n                                        onClick: function() {\n                                            return handleAction(tool.action);\n                                        },\n                                        title: tool.name,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                            type: \"cms\",\n                                            variant: tool.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 10\n                                        }, _this)\n                                    }, idx, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 9\n                                    }, _this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 498,\n                                columnNumber: 7\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__fixed),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__button), (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().text)),\n                                    title: isEdit ? \"Back\" : \"Edit\",\n                                    onClick: isEdit ? handleBack : handleShowDetail,\n                                    children: isEdit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                        type: \"cms\",\n                                        variant: \"back\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 19\n                                    }, _this) : \"Edit\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 8\n                                }, _this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 7\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                        lineNumber: 497,\n                        columnNumber: 6\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                lineNumber: 436,\n                columnNumber: 4\n            }, _this),\n            isEdit && fixedInfo && editableInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().info), isBuilderMode ? (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().info__builder) : \"\"),\n                style: {\n                    \"--info-cols\": isBuilderMode ? 12 : 4\n                },\n                children: [\n                    isBuilderMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().info__title),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleBack,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                            type: \"cms\",\n                                            variant: \"back\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 10\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 530,\n                                        columnNumber: 9\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h6\", {\n                                        className: \"collect__heading collect__heading--h6\",\n                                        children: \"Media info\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 9\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 529,\n                                columnNumber: 8\n                            }, _this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().info__media),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().body), isEdit ? multiple ? (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().detailed__multi) : (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().detailed) : \"\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MediaItem, {\n                                                media: currentMedia,\n                                                height: \"160px\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 10\n                                            }, _this),\n                                            isEdit && Array.isArray(propsValue) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items__nav),\n                                                        onClick: handlePrevMedia,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                                            type: \"cms\",\n                                                            variant: \"chevron-left\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 13\n                                                        }, _this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 12\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items__list),\n                                                        children: propsValue.map(function(media, idx) {\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items__thumb), idx === currentMediaIdx ? (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().active) : \"\"),\n                                                                onClick: function() {\n                                                                    setCurrentMediaIdx(idx);\n                                                                    // Update shared state if this component is being edited\n                                                                    if (isEdit) {\n                                                                        setMediaInfoState(function(prev) {\n                                                                            return (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_8__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_9__._)({}, prev), {\n                                                                                currentMediaIdx: idx,\n                                                                                currentMedia: propsValue[idx]\n                                                                            });\n                                                                        });\n                                                                    }\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_17__.Image, {\n                                                                    media: media,\n                                                                    alt: \"\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                                    lineNumber: 568,\n                                                                    columnNumber: 15\n                                                                }, _this)\n                                                            }, idx, false, {\n                                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                                lineNumber: 550,\n                                                                columnNumber: 14\n                                                            }, _this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 12\n                                                    }, _this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().items__nav),\n                                                        onClick: handleNextMedia,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                                            type: \"cms\",\n                                                            variant: \"chevron-right\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                            lineNumber: 573,\n                                                            columnNumber: 13\n                                                        }, _this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                        lineNumber: 572,\n                                                        columnNumber: 12\n                                                    }, _this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                lineNumber: 544,\n                                                columnNumber: 11\n                                            }, _this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 9\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__list),\n                                            children: filteredMediaToolbar.map(function(tool, idx) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().toolbar__button),\n                                                    onClick: function() {\n                                                        return handleAction(tool.action);\n                                                    },\n                                                    title: tool.name,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_15__.Icon, {\n                                                        type: \"cms\",\n                                                        variant: tool.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 13\n                                                    }, _this)\n                                                }, idx, false, {\n                                                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 12\n                                                }, _this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 10\n                                        }, _this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 578,\n                                        columnNumber: 9\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 535,\n                                columnNumber: 8\n                            }, _this)\n                        ]\n                    }, void 0, true),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().info__fixed),\n                        children: Object.entries(fixedInfo).map(function(param) {\n                            var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_7__._)(param, 2), key = _param[0], value = _param[1];\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().info__fixed_item),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().info__fixed_label),\n                                        children: key\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 9\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().info__fixed_value),\n                                        children: value\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 9\n                                    }, _this)\n                                ]\n                            }, key, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 597,\n                                columnNumber: 8\n                            }, _this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                        lineNumber: 595,\n                        columnNumber: 6\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().info__editable),\n                        children: Object.entries(editableInfo).map(function(param) {\n                            var _param = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_7__._)(param, 2), key = _param[0], value = _param[1];\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_media_module_scss__WEBPACK_IMPORTED_MODULE_5___default().info__editable_item),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        children: key\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 9\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_18__.Input, {\n                                        type: \"text\",\n                                        className: \"collect__input has__border\",\n                                        name: key,\n                                        value: value || \"\",\n                                        placeholder: key,\n                                        onChange: handleOnChange\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 9\n                                    }, _this)\n                                ]\n                            }, key, true, {\n                                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                                lineNumber: 605,\n                                columnNumber: 8\n                            }, _this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                        lineNumber: 603,\n                        columnNumber: 6\n                    }, _this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_16__.Button, {\n                        className: \"collect__button yellow\",\n                        onClick: handleSaveMediaInfo,\n                        children: \"Save\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                        lineNumber: 618,\n                        columnNumber: 6\n                    }, _this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n                lineNumber: 523,\n                columnNumber: 5\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\Media\\\\Media.tsx\",\n        lineNumber: 435,\n        columnNumber: 3\n    }, _this);\n};\n_s(Media, \"vVOkd+RAZfa1nzskhZ1ZM9WUZ2U=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        _barrel_optimize_names_Button_Icon_Image_Input_useIsomorphicLayoutEffect_collective_core__WEBPACK_IMPORTED_MODULE_10__.useIsomorphicLayoutEffect\n    ];\n});\n_c = Media;\nvar _c;\n$RefreshReg$(_c, \"Media\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Builder/FieldEditor/regular/Media/Media.tsx\n"));

/***/ })

});