"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(pagebuilder)/content-builder/[...slug]/layout",{

/***/ "(app-pages-browser)/./src/contexts/BuilderContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/BuilderContext.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PageBuilderContext: function() { return /* binding */ PageBuilderContext; },\n/* harmony export */   PageBuilderProvider: function() { return /* binding */ PageBuilderProvider; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/_/_object_spread */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread.js\");\n/* harmony import */ var _swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/_/_object_spread_props */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_spread_props.js\");\n/* harmony import */ var _swc_helpers_object_without_properties__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/_/_object_without_properties */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_object_without_properties.js\");\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ObjectUtils_collective_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ObjectUtils!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/utils/object-util.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mock_Builder__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/mock/Builder */ \"(app-pages-browser)/./src/mock/Builder.ts\");\n/* __next_internal_client_entry_do_not_use__ PageBuilderContext,PageBuilderProvider auto */ \n\n\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\nvar PageBuilderContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createContext({});\nvar PageBuilderProvider = function(props) {\n    _s();\n    var _props_value;\n    var propsValue = (_props_value = props.value) !== null && _props_value !== void 0 ? _props_value : {};\n    var _propsValue_globals;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_propsValue_globals = propsValue.globals) !== null && _propsValue_globals !== void 0 ? _propsValue_globals : {}), 1), globals = _useState[0];\n    var _propsValue_data;\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_propsValue_data = propsValue.data) !== null && _propsValue_data !== void 0 ? _propsValue_data : {}), 2), data = _useState1[0], setData = _useState1[1];\n    var _propsValue_contentType;\n    var _useState2 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_propsValue_contentType = propsValue.contentType) !== null && _propsValue_contentType !== void 0 ? _propsValue_contentType : {}), 1), contentType = _useState2[0];\n    var _propsValue_components;\n    var _useState3 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_propsValue_components = propsValue.components) !== null && _propsValue_components !== void 0 ? _propsValue_components : {}), 1), components = _useState3[0];\n    var _propsValue_locale;\n    var _useState4 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_propsValue_locale = propsValue.locale) !== null && _propsValue_locale !== void 0 ? _propsValue_locale : \"en\"), 1), locale = _useState4[0];\n    var _useState5 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(propsValue.configuration), 1), configuration = _useState5[0];\n    var _useState6 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({}), 2), historyChanges = _useState6[0], setHistoryChanges = _useState6[1];\n    var _useState7 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false), 2), isPreview = _useState7[0], setIsPreview = _useState7[1];\n    var _propsValue_slug;\n    var _useState8 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_propsValue_slug = propsValue.slug) !== null && _propsValue_slug !== void 0 ? _propsValue_slug : []), 1), slug = _useState8[0];\n    var _useState9 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        left: true,\n        right: true\n    }), 2), expandedSidebar = _useState9[0], setExpandedSidebar = _useState9[1];\n    var _propsValue_screenTypes;\n    var _useState10 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((_propsValue_screenTypes = propsValue.screenTypes) !== null && _propsValue_screenTypes !== void 0 ? _propsValue_screenTypes : _mock_Builder__WEBPACK_IMPORTED_MODULE_3__.defaultConfig.screenTypes), 1), screenTypes = _useState10[0];\n    var _useState11 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Object.values(screenTypes)[0]), 2), screenSize = _useState11[0], setScreenSize = _useState11[1];\n    var _useState12 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(Date.now()), 2), updatedAt = _useState12[0], setUpdatedAt = _useState12[1];\n    var _useState13 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        key: \"\",\n        id: -1\n    }), 2), editingIden = _useState13[0], setEditingIden = _useState13[1];\n    var _useState14 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]), 2), childComponentData = _useState14[0], setChildComponentData = _useState14[1];\n    var _useState15 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"), 2), layerPos = _useState15[0], setLayerPos = _useState15[1];\n    var _useState16 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_2__._)((0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isActive: false,\n        mediaId: null,\n        fixedInfo: {\n            size: \"\",\n            dimensions: \"\",\n            date: \"\",\n            extension: \"\"\n        },\n        editableInfo: {\n            fileName: \"\",\n            altText: \"\",\n            caption: \"\"\n        },\n        currentMedia: null,\n        currentMediaIdx: 0,\n        propsValue: null,\n        field: \"\",\n        onChange: undefined\n    }), 2), mediaInfoState = _useState16[0], setMediaInfoState = _useState16[1];\n    // Normalize data input\n    var normalizedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function() {\n        var commonData = data.data;\n        var orgData = commonData !== null && commonData !== void 0 ? commonData : {};\n        var _ObjectUtils_elevateProperty = _barrel_optimize_names_ObjectUtils_collective_core__WEBPACK_IMPORTED_MODULE_4__.ObjectUtils.elevateProperty(orgData, \"data\"), components = _ObjectUtils_elevateProperty.components, _$props = (0,_swc_helpers_object_without_properties__WEBPACK_IMPORTED_MODULE_5__._)(_ObjectUtils_elevateProperty, [\n            \"components\"\n        ]);\n        var isTempKeyExisted = components.some(function(component) {\n            return component.__temp_key__;\n        });\n        return (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_6__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_7__._)({}, _$props), {\n            components: isTempKeyExisted ? components : components.map(function(component, index) {\n                return (0,_swc_helpers_object_spread_props__WEBPACK_IMPORTED_MODULE_6__._)((0,_swc_helpers_object_spread__WEBPACK_IMPORTED_MODULE_7__._)({}, component), {\n                    __temp_key__: index + 1\n                });\n            })\n        });\n    }, [\n        data\n    ]);\n    var value = {\n        globals: globals,\n        components: components,\n        data: data,\n        setData: setData,\n        contentType: contentType,\n        locale: locale,\n        configuration: configuration,\n        historyChanges: historyChanges,\n        setHistoryChanges: setHistoryChanges,\n        isPreview: isPreview,\n        setIsPreview: setIsPreview,\n        slug: slug,\n        expandedSidebar: expandedSidebar,\n        setExpandedSidebar: setExpandedSidebar,\n        screenTypes: screenTypes,\n        screenSize: screenSize,\n        setScreenSize: setScreenSize,\n        updatedAt: updatedAt,\n        setUpdatedAt: setUpdatedAt,\n        editingIden: editingIden,\n        setEditingIden: setEditingIden,\n        normalizedData: normalizedData,\n        childComponentData: childComponentData,\n        setChildComponentData: setChildComponentData,\n        layerPos: layerPos,\n        setLayerPos: setLayerPos\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PageBuilderContext.Provider, {\n        value: value,\n        children: props.children\n    }, void 0, false, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\contexts\\\\BuilderContext.tsx\",\n        lineNumber: 171,\n        columnNumber: 9\n    }, _this);\n};\n_s(PageBuilderProvider, \"XAgd9M9G1YWI7xigHP1X5+shAoY=\");\n_c = PageBuilderProvider;\nvar _c;\n$RefreshReg$(_c, \"PageBuilderProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/BuilderContext.tsx\n"));

/***/ })

});