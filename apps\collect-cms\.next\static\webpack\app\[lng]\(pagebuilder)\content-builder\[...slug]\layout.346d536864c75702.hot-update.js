"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[lng]/(pagebuilder)/content-builder/[...slug]/layout",{

/***/ "(app-pages-browser)/./src/components/Builder/FieldEditor/regular/ColorPicker/ColorPicker.tsx":
/*!********************************************************************************!*\
  !*** ./src/components/Builder/FieldEditor/regular/ColorPicker/ColorPicker.tsx ***!
  \********************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ColorPicker: function() { return /* binding */ ColorPicker; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/_/_sliced_to_array */ \"(app-pages-browser)/../../node_modules/@swc/helpers/esm/_sliced_to_array.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_Input_collective_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Input!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Input/Input.js\");\n/* harmony import */ var _barrel_optimize_names_Icon_Input_collective_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Icon,Input!=!@collective/core */ \"(app-pages-browser)/../../packages/core/dist/components/Icon/Icon.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ \"(app-pages-browser)/../../node_modules/classnames/index.js\");\n/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_colorful__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-colorful */ \"(app-pages-browser)/../../node_modules/react-colorful/dist/index.mjs\");\n/* harmony import */ var _colorpicker_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./colorpicker.module.scss */ \"(app-pages-browser)/./src/components/Builder/FieldEditor/regular/ColorPicker/colorpicker.module.scss\");\n/* harmony import */ var _colorpicker_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_colorpicker_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nvar ColorPicker = function(props) {\n    _s();\n    var required = props.required, value = props.value, onChange = props.onChange, name = props.name, placeholder = props.placeholder;\n    var _useState = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(value !== null && value !== void 0 ? value : \"#000000\"), 2), propsValue = _useState[0], setPropsValue = _useState[1];\n    var _useState1 = (0,_swc_helpers_sliced_to_array__WEBPACK_IMPORTED_MODULE_4__._)((0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), 2), isFocused = _useState1[0], setIsFocused = _useState1[1];\n    var inputRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    var handleFocus = function() {\n        return setIsFocused(true);\n    };\n    var handleBlur = function() {\n        return setIsFocused(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_colorpicker_module_scss__WEBPACK_IMPORTED_MODULE_3___default().wrapper),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Input_collective_core__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                type: \"text\",\n                className: \"collect__input has__border\",\n                required: required,\n                value: propsValue,\n                placeholder: placeholder,\n                maxLength: 7,\n                onFocus: handleFocus,\n                onBlur: handleBlur,\n                onChange: function(e) {\n                    setPropsValue(e.target.value);\n                    onChange === null || onChange === void 0 ? void 0 : onChange({\n                        field: name,\n                        value: e.target.value\n                    });\n                },\n                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_colorpicker_module_scss__WEBPACK_IMPORTED_MODULE_3___default().color__dot),\n                    style: {\n                        \"--color\": propsValue\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\ColorPicker\\\\ColorPicker.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 6\n                }, void 0),\n                endIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Icon_Input_collective_core__WEBPACK_IMPORTED_MODULE_6__.Icon, {\n                    type: \"cms\",\n                    variant: \"edit\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\ColorPicker\\\\ColorPicker.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 14\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\ColorPicker\\\\ColorPicker.tsx\",\n                lineNumber: 24,\n                columnNumber: 4\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: classnames__WEBPACK_IMPORTED_MODULE_1___default()((_colorpicker_module_scss__WEBPACK_IMPORTED_MODULE_3___default().color__picker), isFocused ? (_colorpicker_module_scss__WEBPACK_IMPORTED_MODULE_3___default().active) : \"\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_colorful__WEBPACK_IMPORTED_MODULE_7__.HexColorPicker, {\n                    color: propsValue,\n                    onChange: function(color) {\n                        console.log(color);\n                        setPropsValue(color);\n                        onChange === null || onChange === void 0 ? void 0 : onChange({\n                            field: name,\n                            value: color\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\ColorPicker\\\\ColorPicker.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 5\n                }, _this)\n            }, void 0, false, {\n                fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\ColorPicker\\\\ColorPicker.tsx\",\n                lineNumber: 45,\n                columnNumber: 4\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\CDA\\\\repos\\\\brand-compass-frontend-template\\\\apps\\\\collect-cms\\\\src\\\\components\\\\Builder\\\\FieldEditor\\\\regular\\\\ColorPicker\\\\ColorPicker.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, _this);\n};\n_s(ColorPicker, \"RqhyL5aVed1rhcIfRbYb9bs2RZA=\");\n_c = ColorPicker;\nvar _c;\n$RefreshReg$(_c, \"ColorPicker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Builder/FieldEditor/regular/ColorPicker/ColorPicker.tsx\n"));

/***/ })

});